// 视频URL刷新功能测试脚本
// 在浏览器控制台中运行此脚本来测试功能

console.log('开始测试视频URL刷新功能...');

// 测试用的MinIO预签名URL示例
const testUrls = [
  'http://localhost:9000/bucket/video1.mp4?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=minioadmin%2F20241212%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20241212T062734Z&X-Amz-Expires=3600&X-Amz-SignedHeaders=host&X-Amz-Signature=abc123',
  'http://localhost:9000/bucket/video2.mp4',
  'https://example.com/video3.mp4?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Signature=def456',
  'https://regular-cdn.com/video4.mp4'
];

// 测试isMinioPresignedUrl函数
function testIsMinioPresignedUrl() {
  console.log('\n=== 测试 isMinioPresignedUrl 函数 ===');
  
  const isMinioPresignedUrl = (url) => {
    if (!url) return false;
    return url.includes('X-Amz-Algorithm') || url.includes('X-Amz-Credential') || url.includes('X-Amz-Signature');
  };
  
  testUrls.forEach((url, index) => {
    const result = isMinioPresignedUrl(url);
    console.log(`URL ${index + 1}: ${result ? '✓ 预签名URL' : '✗ 普通URL'}`);
    console.log(`  ${url.substring(0, 80)}${url.length > 80 ? '...' : ''}`);
  });
}

// 测试extractObjectNameFromUrl函数
function testExtractObjectNameFromUrl() {
  console.log('\n=== 测试 extractObjectNameFromUrl 函数 ===');
  
  const extractObjectNameFromUrl = (url) => {
    if (!url) return null;
    try {
      const urlObj = new URL(url);
      return urlObj.pathname.substring(1);
    } catch (error) {
      console.error('解析URL失败:', error);
      return null;
    }
  };
  
  testUrls.forEach((url, index) => {
    const objectName = extractObjectNameFromUrl(url);
    console.log(`URL ${index + 1} 对象名称: ${objectName || '无法解析'}`);
  });
}

// 模拟视频播放错误处理
function testVideoErrorHandling() {
  console.log('\n=== 测试视频错误处理 ===');
  
  // 模拟video元素和错误事件
  const mockVideoElement = {
    src: testUrls[0],
    load: () => console.log('重新加载视频'),
    addEventListener: (event, handler) => console.log(`添加事件监听器: ${event}`)
  };
  
  const mockVideoItem = {
    id: 'test-video-1',
    vidUrl: testUrls[0]
  };
  
  const mockEvent = {
    target: mockVideoElement,
    type: 'error'
  };
  
  console.log('模拟视频加载错误...');
  console.log('原始URL:', mockVideoItem.vidUrl);
  console.log('错误处理: 尝试刷新URL并重新加载视频');
}

// 测试URL缓存机制
function testUrlCache() {
  console.log('\n=== 测试URL缓存机制 ===');
  
  const urlCache = new Map();
  const cacheKey = 'video_test-1';
  const testUrl = 'http://localhost:9000/bucket/refreshed-video.mp4';
  
  // 设置缓存
  urlCache.set(cacheKey, {
    url: testUrl,
    timestamp: Date.now()
  });
  
  console.log('设置缓存:', cacheKey);
  
  // 检查缓存
  if (urlCache.has(cacheKey)) {
    const cached = urlCache.get(cacheKey);
    const isExpired = Date.now() - cached.timestamp > 5 * 60 * 1000; // 5分钟
    console.log('缓存存在:', cached.url);
    console.log('是否过期:', isExpired ? '是' : '否');
  }
}

// 运行所有测试
function runAllTests() {
  console.log('🚀 视频URL刷新功能测试开始');
  
  testIsMinioPresignedUrl();
  testExtractObjectNameFromUrl();
  testVideoErrorHandling();
  testUrlCache();
  
  console.log('\n✅ 所有测试完成！');
  console.log('\n📝 使用说明:');
  console.log('1. 页面加载时会自动刷新所有预签名URL');
  console.log('2. 视频播放失败时会自动尝试刷新URL');
  console.log('3. 可以点击"刷新视频"按钮手动刷新所有视频');
  console.log('4. 可以点击单个视频的"刷新"按钮刷新特定视频');
  console.log('5. 系统会缓存URL 5分钟，避免频繁请求');
}

// 如果在浏览器环境中运行
if (typeof window !== 'undefined') {
  runAllTests();
} else {
  // 如果在Node.js环境中运行
  module.exports = {
    testIsMinioPresignedUrl,
    testExtractObjectNameFromUrl,
    testVideoErrorHandling,
    testUrlCache,
    runAllTests
  };
}
