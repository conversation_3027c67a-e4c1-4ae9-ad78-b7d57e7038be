{"name": "renren-ui", "version": "5.4.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "npm run build:prod", "build:prod": "vue-tsc --noEmit && vite build --mode production", "serve": "npm run build && vite preview", "lint": "eslint \"src/**/*.{vue,ts}\" --fix"}, "gitHooks": {"pre-commit": "lint-staged"}, "lint-staged": {"src/**/*.{ts,vue}": ["eslint --fix", "git add"]}, "dependencies": {"@dataview/datav-vue3": "^0.0.0-test.1672506674342", "@element-plus/icons-vue": "2.3.1", "@jiaminghi/data-view": "^2.10.0", "@vueuse/core": "9.1.1", "@wangeditor/editor": "5.1.1", "@wangeditor/editor-for-vue": "^5.1.12", "axios": "1.6.0", "classnames": "^2.3.1", "core-js": "^3.14.0", "echarts": "^5.6.0", "echarts-wordcloud": "^2.1.0", "element-plus": "^2.7.6", "lodash": "^4.17.21", "mitt": "^2.1.0", "nprogress": "^0.2.0", "pinia": "2.1.7", "qs": "^6.10.1", "quill": "^1.3.7", "video.js": "^7.9.0", "vue": "^3.4.31", "vue-echarts": "^6.7.3", "vue-router": "4.2.5", "vue-video-player": "^6.0.0"}, "devDependencies": {"@types/lodash": "^4.14.172", "@types/nprogress": "^0.2.0", "@types/qs": "^6.9.6", "@types/sortablejs": "^1.10.6", "@typescript-eslint/eslint-plugin": "^5.23.0", "@typescript-eslint/parser": "^5.23.0", "@vitejs/plugin-vue": "5.0.5", "@vue/compiler-sfc": "^3.4.31", "@vue/eslint-config-prettier": "^7.0.0", "@vue/eslint-config-typescript": "^10.0.0", "eslint": "^8.13.0", "eslint-plugin-vue": "^8.6.0", "less": "^4.1.1", "less-loader": "^10.0.0", "lint-staged": "^11.0.0", "prettier": "^2.6.2", "sass": "^1.50.1", "typescript": "^4.6.3", "unplugin-auto-import": "^0.18.3", "unplugin-vue-components": "^0.27.4", "vite": "5.2.11", "vite-plugin-html": "^3.2.2", "vite-plugin-svg-icons": "2.0.1", "vite-tsconfig-paths": "3.4.0", "vue-tsc": "2.0.16"}}