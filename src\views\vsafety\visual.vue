<template>
	<div class="visual-analysis">
		<el-row :gutter="32">
			<el-col :span="12">
				<div class="chart-card">
					<div class="chart-title">事件分类占比</div>
					<v-chart :option="eventCategoryOption" autoresize style="height:320px;" />
				</div>
			</el-col>
			<el-col :span="12">
				<div class="chart-card">
					<div class="chart-title">事件分类-情感倾向性占比</div>
					<v-chart :option="sentimentCategoryOption" autoresize style="height:320px;" />
				</div>
			</el-col>
		</el-row>
		<div class="tagcloud-card">
			<div class="chart-title">关键词热图</div>
			<!-- 使用ECharts实现词云 -->
			<v-chart 
				:option="tagCloudOption" 
				autoresize 
				style="height:400px;" 
				@click="handleKeywordClick"
			/>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import { ElRow, ElCol } from 'element-plus';
import VChart from 'vue-echarts';
import 'echarts';
// 引入ECharts词云组件
import 'echarts-wordcloud';
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";

// 事件分类占比数据
const eventCategoryData = [
	{ value: 20, name: '社会舆情' },
	{ value: 10, name: '政治舆情' },
	{ value: 2, name: '经济舆情' },
	{ value: 4, name: '文化舆情' }
];

const eventCategoryOption = {
	tooltip: { trigger: 'item' },
	legend: { top: 'bottom' },
	series: [
		{
			name: '事件分类',
			type: 'pie',
			radius: '60%',
			data: eventCategoryData,
			label: { formatter: '{b}: {d}%' }
		}
	]
};

// 情感倾向性占比数据
const sentimentCategoryData = [
	{ value: 30, name: '社会舆情-正面' },
	{ value: 10, name: '社会舆情-负面' },
	{ value: 20, name: '政治舆情-中性' },
	{ value: 15, name: '经济舆情-正面' },
	{ value: 10, name: '文化舆情-负面' },
	{ value: 15, name: '经济舆情-中性' }
];

const sentimentCategoryOption = {
	tooltip: { trigger: 'item' },
	legend: { top: 'bottom' },
	series: [
		{
			name: '事件分类-情感',
			type: 'pie',
			radius: '60%',
			data: sentimentCategoryData,
			label: { formatter: '{b}: {d}%' }
		}
	]
};

// 关键词数据与词云配置
const tagCloudData = ref<Array<{name: string, value: number}>>([]);
const tagCloudOption = ref({
	tooltip: {
		show: true,
		formatter: function(params: any) {
			return `${params.name}<br>权重: ${params.value}`;
		},
		backgroundColor: 'rgba(0,0,0,0.7)',
		textStyle: { color: '#fff' },
		padding: 10,
		borderRadius: 4
	},
	series: [
		{
			type: 'wordCloud',
			// 词云位置与大小
			left: 'center',
			top: 'center',
			width: '100%',
			height: '100%',
			// 词云配置
			sizeRange: [25, 60], // 增大字体大小范围，让关键词显示更大
			rotationRange: [-30, 30], // 旋转角度范围
			rotationStep: 30, // 旋转角度步长
			gridSize: 6, // 减小词间距，让关键词更密集
			// 形状可选: 'circle', 'cardioid', 'diamond', 'triangle-forward', 'triangle', 'star'
			shape: 'circle',
			// 文本样式
			textStyle: {
				fontFamily: 'system-ui, sans-serif',
				fontWeight: 'bold',
				// 颜色映射（权重越高颜色越深）
				color: function(params: any) {
					const colorList = [
						'#ff7e5f', '#feb47b', '#ffcc5c', 
						'#a8e6cf', '#89daff', '#dcedc1'
					];
					return colorList[Math.floor(params.dataIndex % colorList.length)];
				}
			},
			// 悬停样式
			emphasis: {
				focus: 'self',
				textStyle: {
					color: '#409eff',
					shadowBlur: 10,
					shadowColor: '#333'
				}
			},
			data: []
		}
	]
});

// 关键词点击事件
const handleKeywordClick = (params: any) => {
	ElMessage.success(`选中关键词: ${params.name}，权重: ${params.value}`);
	// 可扩展：根据关键词筛选相关数据
};

// 提取关键词方法
const extractKeywords = (analysisResult: any): Array<{name: string, value: number}> => {
	try {
		let parsed;
		if (typeof analysisResult === 'string') {
			parsed = JSON.parse(analysisResult);
		} else {
			parsed = analysisResult;
		}
		
		const keywordsMap: {[key: string]: number} = {};
		let tagsData: any[] = [];
		
		// 处理数据结构
		if (Array.isArray(parsed) && parsed.length > 0) {
			const firstItem = parsed[0];
			if (firstItem.tags) tagsData = firstItem.tags;
		} else if (parsed.tags) {
			tagsData = parsed.tags;
		}
		
		// 统计关键词出现次数作为权重
		if (Array.isArray(tagsData)) {
			tagsData.forEach(tag => {
				if (typeof tag === 'string') {
					keywordsMap[tag] = (keywordsMap[tag] || 0) + 1;
				}
			});
		}
		
		// 转换为ECharts词云所需格式
		return Object.entries(keywordsMap)
			.map(([name, value]) => ({ name, value }))
			.sort((a, b) => b.value - a.value)
			.slice(0, 50);
	} catch (error) {
		console.error('解析关键词数据出错:', error);
		return [];
	}
};

// 获取所有关键词数据
const fetchAllKeywords = async () => {
	try {
		// 获取图片分析数据
		const pictureRes = await baseService.get("/vsafety/picture/page", { page: 1, limit: 1000 });
		const pictureKeywords = pictureRes.data?.list
			.filter((item: any) => item.analysisResult)
			.flatMap((item: any) => extractKeywords(item.analysisResult));
		
		// 获取视频分析数据
		const videoRes = await baseService.get("/vsafety/video/page", { page: 1, limit: 1000 });
		const videoKeywords = videoRes.data?.list
			.filter((item: any) => item.analysisResult)
			.flatMap((item: any) => extractKeywords(item.analysisResult));
		
		// 获取音频分析数据
		const audioRes = await baseService.get("/vsafety/audio/page", { page: 1, limit: 1000 });
		const audioKeywords = audioRes.data?.list
			.filter((item: any) => item.analysisResult)
			.flatMap((item: any) => extractKeywords(item.analysisResult));
		
		// 合并所有关键词并统计权重
		const allKeywordsMap: {[key: string]: number} = {};
		
		[...(pictureKeywords || []), ...(videoKeywords || []), ...(audioKeywords || [])].forEach(keyword => {
			allKeywordsMap[keyword.name] = (allKeywordsMap[keyword.name] || 0) + keyword.value;
		});
		
		// 转换为数组并按权重排序
		tagCloudData.value = Object.entries(allKeywordsMap)
			.map(([name, value]) => ({ name, value }))
			.sort((a, b) => b.value - a.value)
			.slice(0, 100);
	} catch (error) {
		console.error("获取关键词数据失败:", error);
		ElMessage.error("获取关键词数据失败");
	}
};

// 监听关键词数据变化，更新词云
watch(tagCloudData, (newData) => {
	tagCloudOption.value.series[0].data = newData;
});

onMounted(() => {
	fetchAllKeywords();
});
</script>

<style scoped>
.visual-analysis {
	padding: 32px;
	background: #fff;
	border-radius: 8px;
}
.chart-card {
	background: #f5f7fa;
	border-radius: 8px;
	box-shadow: 0 1px 6px rgba(0,0,0,0.08);
	padding: 24px 16px 16px 16px;
	margin-bottom: 24px;
}
.chart-title {
	font-size: 18px;
	font-weight: 600;
	margin-bottom: 16px;
	color: #303133;
	/* 标题样式优化 */
	padding-left: 8px;
	border-left: 4px solid #409eff;
}
.tagcloud-card {
	background: #f5f7fa;
	border-radius: 8px;
	box-shadow: 0 1px 6px rgba(0,0,0,0.08);
	padding: 24px 16px 32px 16px;
	margin-top: 32px;
	position: relative;
	height: 500px; /* 增加卡片高度，让词云有更多显示空间 */
}
/* 响应式调整 */
@media (max-width: 768px) {
	.visual-analysis {
		padding: 16px;
	}
	.tagcloud-card {
		padding: 16px;
		height: 40vh; /* 响应式高度调整 */
	}
	.chart-title {
		font-size: 16px;
	}
}
</style>
