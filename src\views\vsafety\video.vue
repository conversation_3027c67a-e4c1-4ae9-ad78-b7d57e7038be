<template>
  <div class="header">
    <el-header>
      <!-- 添加查询选择 -->
      <el-form :inline="true" :model="dataForm" @keyup.enter="getData()">
        <!-- 新增的筛选查询组件 -->
        <el-form-item>
          <el-select v-model="dataForm.sentimentAnalysis" placeholder="请选择情感倾向性" clearable>
            <el-option label="正面" value="正面"></el-option>
            <el-option label="负面" value="负面"></el-option>
            <el-option label="中性" value="中性"></el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <el-select v-model="dataForm.eventCategory" placeholder="请选择事件分类" clearable>
            <el-option label="政治舆情" value="政治舆情"></el-option>
            <el-option label="经济舆情" value="经济舆情"></el-option>
            <el-option label="文化舆情" value="文化舆情"></el-option>
            <el-option label="社会舆情" value="社会舆情"></el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="getData()" :icon="Search">
            查询
          </el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="success" @click="resetQuery()" :icon="Refresh">
            重置
          </el-button>
        </el-form-item>
        <!-- 添加刷新视频URL按钮 -->
        <el-form-item>
          <el-button type="info" @click="refreshAllVideoUrls()" :icon="Refresh">
            刷新视频
          </el-button>
        </el-form-item>
        <!-- 新增按钮 -->
        <el-form-item>
          <el-button type="primary" @click="addOrUpdateHandle()" :icon="Plus">
            新增
          </el-button>
        </el-form-item>
      </el-form>
    </el-header>
  </div>
  <div class="container">
    <el-container>
      <el-main v-loading="loading">
        <div v-show="videoList.length <= 0" style="text-align: center">暂无数据 !</div>
        <div class="video-grid">
          <div v-for="item in videoList" :key="item.id" class="video-box" 
               @click="handleDialog(item.id, item.context, getPlatform(item.platformId), getLabel(item.labelId), getRisk(item.riskId), getRiskColor(item.riskId))">
            <div class="video-thumb">
              <video
                v-if="item.vidUrl"
                :src="item.vidUrl"
                controls
                preload="auto"
                class="video-player"
                @click.stop="handleDialog(item.id, item.context, getPlatform(item.platformId), getLabel(item.labelId), getRisk(item.riskId), getRiskColor(item.riskId))"
                @error="handleVideoError($event, item)"
                @loadstart="() => {}"
              ></video>
              <div v-else class="no-video">无视频</div>
              <!-- 视频刷新状态显示 -->
              <div v-if="refreshingUrls.has(item.id)" class="video-refreshing">
                <el-icon class="is-loading"><Refresh /></el-icon>
                <span>刷新中...</span>
              </div>
              <!-- 将舆论情感移到视频左上角 -->
              <div v-if="getFirstEventInfo(item.analysisResult)?.public_opinion_sentiment" 
                   class="sentiment-tag"
                   :class="getFirstEventInfo(item.analysisResult)?.public_opinion_sentiment === '正面' ? 'sentiment-positive' : 
                           getFirstEventInfo(item.analysisResult)?.public_opinion_sentiment === '负面' ? 'sentiment-negative' : 
                           'sentiment-neutral'">
                {{ getFirstEventInfo(item.analysisResult)?.public_opinion_sentiment }}
              </div>
            </div>
            <!-- 简化显示分析结果信息 -->
            <div class="analysis-info">
              <div class="info-item">
                <span class="label" >标题:</span>
                <span class="title-text">{{ getFirstEventInfo(item.analysisResult)?.event_title || '无' }}</span>
              </div>
              <div class="info-item">
                <span class="label">舆情分类:</span>
                <el-tag type="primary" size="small">
                  {{ getFirstEventInfo(item.analysisResult)?.public_opinion_category || '无' }}
                </el-tag>
              </div>
            </div>
            <!-- 操作按钮 -->
            <div class="video-actions">
              <el-button
                v-if="item.vidUrl && isMinioPresignedUrl(item.vidUrl)"
                type="info"
                size="small"
                @click.stop="refreshVideoUrl(item)"
                :icon="Refresh"
                link
                class="action-btn"
                :loading="refreshingUrls.has(item.id)"
                title="刷新视频链接"
              >
                <span style="color: #409eff;">刷新</span>
              </el-button>
              <el-button
                type="danger"
                size="small"
                @click.stop="deleteHandle(item.id)"
                :icon="Delete"
                link
                class="action-btn"
              >
                <span style="color: #f56c6c;">删除</span>
              </el-button>
            </div>
          </div>
        </div>
      </el-main>
    </el-container>
    <!-- 弹窗 舆情详情 -->
    <po-detail-dialog ref="poDetailDialog" @refresh-video-list="getData"></po-detail-dialog>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update ref="addOrUpdateRef" @refreshDataList="getData"></add-or-update>
    
    <!-- 视频上传弹窗 -->
    <el-dialog 
      v-model="uploadDialogVisible" 
      title="上传视频" 
      width="30%"
      :close-on-click-modal="false"
    >
      <div class="upload-container">
        <input 
          ref="fileInputRef" 
          type="file" 
          accept="video/*" 
          @change="handleFileChange" 
          style="display: none"
        />
        <div class="upload-area" @click="triggerFileInput" v-if="!selectedFile">
          <el-icon class="upload-icon"><Plus /></el-icon>
          <p>点击选择视频文件</p>
        </div>
        <div class="file-selected" v-else>
          <el-icon class="file-icon"><Document /></el-icon>
          <div class="file-info">
            <p class="file-name">{{ selectedFileName }}</p>
            <p class="file-size">{{ formatFileSize(selectedFile.size) }}</p>
          </div>
          <el-icon class="change-icon" @click.stop="triggerFileInput"><Refresh /></el-icon>
        </div>
      </div>
      <template #footer>
        <el-button @click="uploadDialogVisible = false">取消</el-button>
        <el-button 
          type="primary" 
          @click="uploadVideo" 
          :loading="uploadLoading"
          :disabled="!selectedFile"
        >
          上传
        </el-button>
      </template>
    </el-dialog>
  </div>
  <div class="demo-pagination-block">
    <el-pagination
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :page-sizes="[10, 20, 50, 100]"
      :size="size"
      :disabled="disabled"
      :background="background"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script setup>
import { onMounted, ref, reactive, toRefs } from "vue";
import baseService from "@/service/baseService";
import PoDetailDialog from "/src/views/yuqing/po-detail-dialog.vue";
import useView from "@/hooks/useView";
import AddOrUpdate from "./video-add-or-update.vue";
// 引入图标组件
import { Search, Refresh, Plus, Delete, Document } from '@element-plus/icons-vue'
import { ElMessage } from "element-plus";
// 引入预签名URL相关API
import { getBatchPresignedUrls, getPresignedUrl } from "@/utils/api";

// 配置useView
const view = reactive({
  deleteIsBatch: true,
  deleteURL: "/vsafety/video",
  deleteIsBatchKey: "id"
});

const state = reactive({ ...useView(view), ...toRefs(view) });

const loading = ref(false);
const dateList = ref([]);
const videoList = ref([]);
const currentPage = ref(1);
const pageSize = ref(10);
const size = ref("default");
const background = ref(true);
const disabled = ref(false);
const total = ref(0);
const poDetailDialog = ref();
const addOrUpdateRef = ref();

// 视频上传相关状态
const uploadDialogVisible = ref(false);
const fileInputRef = ref();
const selectedFile = ref(null);
const selectedFileName = ref("");
const uploadLoading = ref(false);

// 添加查询表单数据
const dataForm = ref({
  sentimentAnalysis: '',
  eventCategory: ''
});

// 视频URL刷新相关状态
const refreshingUrls = ref(new Set()); // 正在刷新URL的视频ID集合
const urlCache = ref(new Map()); // URL缓存，避免重复请求

const getPlatform = (platformId) => {
  const platform = {
    "-1": "未指定",
    1: "微博",
    2: "微信",
    3: "抖音",
  };
  return platform[platformId];
};

const getLabel = (labelId) => {
  const label = {
    0: "人身伤害",
    1: "交通事故",
    2: "自然灾害",
    3: "校园事件",
    4: "医疗卫生",
    5: "环境污染",
    6: "国家安全",
    7: "宗教文化",
    8: "其他负面",
    9: "非负样本",
    "-1": "未指定"
  };
  return label[labelId];
};

const getRisk = (riskId) => {
  const risk = {
    0: "低风险",
    1: "中风险",
    2: "高风险",
    "-1": "未指定"
  };
  return risk[riskId];
};

const getRiskColor = (riskId) => {
  const color = {
    0: "darkturquoise",
    1: "orange",
    2: "red"
  };
  return color[riskId];
};

// 添加重置查询方法
const resetQuery = () => {
  dataForm.value.sentimentAnalysis = '';
  dataForm.value.eventCategory = '';
  getData();
};

// 添加解析分析结果的方法
const getFirstEventInfo = (analysisResult) => {
  if (!analysisResult) return null;
  
  try {
    let parsed;
    if (typeof analysisResult === 'string') {
      parsed = JSON.parse(analysisResult);
    } else {
      parsed = analysisResult;
    }
    
    // 处理新的数据结构
    if (Array.isArray(parsed) && parsed.length > 0) {
      const firstItem = parsed[0];
      if (firstItem.events && Array.isArray(firstItem.events) && firstItem.events.length > 0) {
        return firstItem.events[0];
      }
    }
    
    // 保持原有逻辑以兼容旧数据结构
    if (Array.isArray(parsed) && parsed.length > 0 && parsed[0].events) {
      const events = parsed[0].events;
      if (Array.isArray(events) && events.length > 0) {
        return events[0];
      }
    }
    
    return null;
  } catch (error) {
    console.error('解析分析结果出错:', error);
    return null;
  }
};

function mergeVidUrlToDateList(dateArr, videoArr) {
  return dateArr.map(item => {
    const video = videoArr.find(v => v.id === item.videoId);
    return {
      ...item,
      vidUrl: video ? video.vidUrl : null
    };
  });
}

const getData = async () => {
  loading.value = true;
  // 判断是否需要调用分类查询接口
  if (dataForm.value.sentimentAnalysis || dataForm.value.eventCategory) {
    // 调用分类查询接口
    await getCategoryList();
  } else {
    // 调用原始分页接口
    await getDefaultList();
  }
  // 数据加载完成后，刷新视频URL
  await refreshAllVideoUrls();
};

// 新增分类查询方法
const getCategoryList = () => {
  const params = {
    page: currentPage.value,
    limit: pageSize.value,
    ...dataForm.value
  };

  return baseService.post("/vsafety/video/categoryPage", params).then((res) => {
    videoList.value = res.data.list;
    total.value = res.data.total;
    loading.value = false;
    return res;
  }).catch((error) => {
    console.error("获取视频数据失败:", error);
    videoList.value = [];
    total.value = 0;
    loading.value = false;
    throw error;
  });
};

// 原始分页查询方法
const getDefaultList = () => {
  const params = {
    page: currentPage.value,
    limit: pageSize.value,
    ...dataForm.value
  };

  return baseService.get("/vsafety/video/page", params).then((res) => {
    videoList.value = res.data.list;
    total.value = res.data.total;
    loading.value = false;
    return res;
  }).catch((error) => {
    console.error("获取视频数据失败:", error);
    videoList.value = [];
    total.value = 0;
    loading.value = false;
    throw error;
  });
};

/* 当前页改变时的回调*/
const handleCurrentChange = (val) => {
  currentPage.value = val;
  getData();
};

/* 每页条数改变时的回调*/
const handleSizeChange = (val) => {
  currentPage.value = 1;
  pageSize.value = val;
  getData();
};

// 修改 handleDialog 方法以传递正确的参数
const handleDialog = (id, context, resource, type, grade, color) => {
  const detailParams = {
    id,
    context,
    resource,
    type,
    grade,
    color
  };
  poDetailDialog.value.init(detailParams);
};

// 新增/修改处理
const addOrUpdateHandle = (id) => {
  // 如果有id，则是修改操作，使用原来的弹窗
  if (id) {
    addOrUpdateRef.value.init(id);
  } else {
    // 否则是新增操作，打开上传弹窗
    uploadDialogVisible.value = true;
    selectedFile.value = null;
    selectedFileName.value = "";
  }
};

// 删除处理
const deleteHandle = (id) => {
  state.deleteHandle(id).then(() => {
    // 删除成功后重新获取数据
    getData();
  });
};

// 触发文件选择
const triggerFileInput = () => {
  if (fileInputRef.value) {
    fileInputRef.value.click();
  }
};

// 处理文件选择
const handleFileChange = (event) => {
  const input = event.target;
  if (input.files && input.files.length > 0) {
    const file = input.files[0];
    
    // 验证文件类型是否为视频文件
    const allowedTypes = [
      'video/mp4', 'video/avi', 'video/mov', 'video/wmv', 
      'video/flv', 'video/mkv', 'video/webm'
    ];
    
    const allowedExtensions = [
      '.mp4', '.avi', '.mov', '.wmv', '.flv', '.mkv', '.webm'
    ];
    
    // 检查 MIME 类型或文件扩展名
    const isAllowedType = allowedTypes.includes(file.type);
    const isAllowedExtension = allowedExtensions.some(ext => 
      file.name.toLowerCase().endsWith(ext)
    );
    
    if (!isAllowedType && !isAllowedExtension) {
      ElMessage.error("请选择有效的视频文件 (mp4, avi, mov, wmv, flv, mkv, webm)");
      // 清空文件输入框
      input.value = '';
      return;
    }
    
    // 检查文件大小 (限制为100MB)
    const isLt100M = file.size / 1024 / 1024 < 100;
    if (!isLt100M) {
      ElMessage.error('视频大小不能超过100MB!');
      input.value = '';
      return;
    }
    
    selectedFile.value = file;
    selectedFileName.value = file.name;
  }
};

// 上传视频文件
const uploadVideo = async () => {
  if (!selectedFile.value) {
    ElMessage.warning("请选择视频文件");
    return;
  }

  uploadLoading.value = true;
  
  try {
    const formData = new FormData();
    formData.append("file", selectedFile.value);
    
    const res = await baseService.post("/vsafety/video/upload", formData);
    
    if (res.code === 0) {
      ElMessage.success("视频上传成功");
      uploadDialogVisible.value = false;
      // 刷新视频分页数据
      getData();
    } else {
      ElMessage.error(res.msg || "视频上传失败");
    }
  } catch (error) {
    ElMessage.error("视频上传失败: " + (error.message || "未知错误"));
  } finally {
    uploadLoading.value = false;
  }
};

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 检测URL是否为MinIO预签名URL
const isMinioPresignedUrl = (url) => {
  if (!url) return false;
  // 检测URL中是否包含MinIO预签名URL的特征参数
  return url.includes('X-Amz-Algorithm') || url.includes('X-Amz-Credential') || url.includes('X-Amz-Signature');
};

// 从视频URL中提取对象名称
const extractObjectNameFromUrl = (url) => {
  if (!url) return null;
  try {
    const urlObj = new URL(url);
    // 移除开头的斜杠，获取对象路径
    return urlObj.pathname.substring(1);
  } catch (error) {
    console.error('解析URL失败:', error);
    return null;
  }
};

// 刷新单个视频的预签名URL
const refreshVideoUrl = async (videoItem) => {
  if (!videoItem.vidUrl || refreshingUrls.value.has(videoItem.id)) {
    return videoItem.vidUrl;
  }

  // 如果不是预签名URL，直接返回原URL
  if (!isMinioPresignedUrl(videoItem.vidUrl)) {
    return videoItem.vidUrl;
  }

  try {
    refreshingUrls.value.add(videoItem.id);

    // 从缓存中获取
    const cacheKey = `video_${videoItem.id}`;
    if (urlCache.value.has(cacheKey)) {
      const cached = urlCache.value.get(cacheKey);
      // 检查缓存是否过期（5分钟）
      if (Date.now() - cached.timestamp < 5 * 60 * 1000) {
        return cached.url;
      }
    }

    // 提取对象名称
    const objectName = extractObjectNameFromUrl(videoItem.vidUrl);
    if (!objectName) {
      console.warn('无法从URL中提取对象名称:', videoItem.vidUrl);
      return videoItem.vidUrl;
    }

    // 获取新的预签名URL
    const response = await getPresignedUrl(objectName);
    if (response.code === 0 && response.data) {
      const newUrl = response.data;

      // 更新缓存
      urlCache.value.set(cacheKey, {
        url: newUrl,
        timestamp: Date.now()
      });

      // 更新视频列表中的URL
      const videoIndex = videoList.value.findIndex(v => v.id === videoItem.id);
      if (videoIndex !== -1) {
        videoList.value[videoIndex].vidUrl = newUrl;
      }

      return newUrl;
    } else {
      console.error('获取预签名URL失败:', response.msg);
      return videoItem.vidUrl;
    }
  } catch (error) {
    console.error('刷新视频URL失败:', error);
    return videoItem.vidUrl;
  } finally {
    refreshingUrls.value.delete(videoItem.id);
  }
};

// 批量刷新视频URL
const refreshAllVideoUrls = async () => {
  if (videoList.value.length === 0) return;

  try {
    // 筛选出需要刷新的视频（MinIO预签名URL）
    const videosNeedRefresh = videoList.value.filter(video =>
      video.vidUrl && isMinioPresignedUrl(video.vidUrl)
    );

    if (videosNeedRefresh.length === 0) return;

    // 提取所有需要刷新的对象名称
    const objectNames = videosNeedRefresh
      .map(video => extractObjectNameFromUrl(video.vidUrl))
      .filter(name => name !== null);

    if (objectNames.length === 0) return;

    // 批量获取预签名URL
    const response = await getBatchPresignedUrls(objectNames);
    if (response.code === 0 && response.data) {
      const urlMap = response.data;

      // 更新视频列表中的URL
      videosNeedRefresh.forEach(video => {
        const objectName = extractObjectNameFromUrl(video.vidUrl);
        if (objectName && urlMap[objectName]) {
          const videoIndex = videoList.value.findIndex(v => v.id === video.id);
          if (videoIndex !== -1) {
            videoList.value[videoIndex].vidUrl = urlMap[objectName];

            // 更新缓存
            const cacheKey = `video_${video.id}`;
            urlCache.value.set(cacheKey, {
              url: urlMap[objectName],
              timestamp: Date.now()
            });
          }
        }
      });

      console.log(`成功刷新 ${Object.keys(urlMap).length} 个视频URL`);
    }
  } catch (error) {
    console.error('批量刷新视频URL失败:', error);
  }
};

// 处理视频加载错误
const handleVideoError = async (event, videoItem) => {
  console.warn('视频加载失败，尝试刷新URL:', videoItem.id);
  const newUrl = await refreshVideoUrl(videoItem);

  // 如果获取到新URL且与原URL不同，重新设置视频源
  if (newUrl && newUrl !== event.target.src) {
    event.target.src = newUrl;
    event.target.load(); // 重新加载视频
  }
};

onMounted(() => {
  getData();
});
</script>

<style scoped>
.el-header {
  background-color: #ebebeb80;
  padding: 10px 15px;
  height: 100%;
  line-height: 3;
}
.el-main {
  background-color: #ebebeb80;
}
.demo-button-style {
  position: relative;
  top: -33px;
  left: 80px;
}
.demo-button-style {
  margin-top: 24px;
}
.header-content {
  display: flex;
  align-items: center;
}
.yuqing-label {
  margin-right: 10px;
  min-width: 70px;
}
.detail-item {
  /* margin-top: 20px; */
  padding: 10px;
  border-radius: 5px;
  /* background-color: #fff; */
}
.detail-title {
  font-size: 15px;
  font-weight: bold;
  margin-bottom: 10px;
  display: flex;
  justify-content: space-between;
}
.detail-content {
  font-size: 14px;
  line-height: 2;
  margin-bottom: 10px;
  display: flex;
  justify-content: space-between;
}
.detail-footer {
  font-size: 12px;
  color: #999;
  margin-top: 10px;
  display: flex;
  justify-content: space-between;
}
.el-tag {
  margin-right: 20px;
  width: 80px;
}
.detail-title-left {
  cursor: pointer;
  color: #677fef;
}
.detail-title-left:hover {
  color: #4060ee;
}
.demo-pagination-block + .demo-pagination-block {
  margin-top: 10px;
}
.demo-pagination-block .demonstration {
  margin-bottom: 16px;
}
.common-header {
  text-align: center;
  margin-bottom: 30px;
}
.search-btn {
  background-color: #5b4cfe !important;
  color: #fff !important;
  border-radius: 0 5px 5px 0 !important;
}
.search-btn:hover {
  background-color: #7f71ff !important;
}
.video-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 24px;
  margin: 30px 0;
}
.video-box {
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 2px 8px #0000000a;
  padding: 16px 12px 12px 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: box-shadow 0.2s;
  cursor: pointer;
  min-height: 260px;
  position: relative;
}
.video-box:hover {
  box-shadow: 0 4px 16px #409eff33;
}
.video-thumb {
  width: 100%;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
  position: relative;
}


.sentiment-tag {
  position: absolute;
  top: 8px;
  left: 8px;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
  color: white;
  z-index: 10;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.sentiment-positive {
  background-color: #67c23a; /* 绿色 */
}

.sentiment-negative {
  background-color: #f56c6c; /* 红色 */
}

.sentiment-neutral {
  background-color: #e6a23c; /* 黄色 */
}

.video-player {
  width: 100%;
  max-height: 120px;
  object-fit: cover;
  border-radius: 6px;
  background: #000;
}
.no-video {
  width: 100%;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #bbb;
  background: #f5f5f5;
  border-radius: 6px;
}

.video-title {
  font-size: 15px;
  font-weight: bold;
  color: #4060ee;
  margin-bottom: 8px;
  cursor: pointer;
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.video-title:hover {
  color: #1a3bb3;
}
.video-meta {
  margin-bottom: 8px;
}
.video-footer {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
  display: flex;
  flex-direction: column;
  gap: 2px;
}
.analysis-info {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 6px;
  padding: 8px 0;
  border-top: 1px solid #eee;
  margin-top: 8px;
}

.info-item {
  font-size: 13px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.info-item .label {
  font-size: 13px;
  color: #666;
  white-space: nowrap;
}

.info-item .el-tag {
  font-size: 12px;
  padding: 0 8px;
  height: 22px;
  line-height: 22px;
}

.video-actions {
  width: 100%;
  display: flex;
  justify-content: flex-end;
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid #eee;
  position: absolute;
  right: 12px;
  bottom: 12px;
  padding: 8px 0 0 0;
  border-top: none;
  z-index: 10;
}

.delete-btn {
  font-size: 12px;
  padding: 4px 12px;
  height: 28px;
  
}

@media (max-width: 1200px) {
  .video-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}
@media (max-width: 800px) {
  .video-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media (max-width: 500px) {
  .video-grid {
    grid-template-columns: 1fr;
  }
}

.upload-container {
  text-align: center;
  padding: 20px 0;
}

.upload-area {
  border: 2px dashed #dcdfe6;
  border-radius: 6px;
  padding: 30px 0;
  cursor: pointer;
  transition: all 0.3s;
  margin-bottom: 20px;
}

.upload-area:hover {
  border-color: #409eff;
  background-color: #f5f9ff;
}

.upload-icon {
  font-size: 48px;
  color: #c0c4cc;
  margin-bottom: 10px;
}

.upload-area p {
  font-size: 14px;
  color: #606266;
  margin: 0;
}

.file-selected {
  display: flex;
  align-items: center;
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  padding: 15px;
  background-color: #f5f7fa;
  margin-bottom: 20px;
}

.file-icon {
  font-size: 32px;
  color: #409eff;
  margin-right: 10px;
}

.file-info {
  flex: 1;
  text-align: left;
}

.file-name {
  font-size: 14px;
  color: #303133;
  margin: 0;
  font-weight: 500;
  word-break: break-all;
}

.file-size {
  font-size: 12px;
  color: #909399;
  margin: 5px 0 0 0;
}

.change-icon {
  font-size: 20px;
  color: #909399;
  cursor: pointer;
  transition: all 0.3s;
  padding: 5px;
  border-radius: 50%;
}

.change-icon:hover {
  color: #409eff;
  background-color: #e4e7ed;
}

/* 视频刷新状态样式 */
.video-refreshing {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
  z-index: 20;
}

.video-refreshing .el-icon {
  font-size: 14px;
}
</style>