<template>
  <div class="mod-vsafety__hotspot">
    <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
      <el-form-item>
        <el-button type="primary" @click="addOrUpdateHandle()" :icon="Plus">
          新增
        </el-button>
      </el-form-item>
      <el-form-item>
        <el-button 
          type="danger" 
          @click="state.deleteHandle()" 
          :icon="Delete"
          :disabled="!state.dataListSelections.length"
        >
          删除
        </el-button>
      </el-form-item>
    </el-form>
    
    <el-table 
      v-loading="state.dataListLoading" 
      :data="state.dataList" 
      border 
      @selection-change="state.dataListSelectionChangeHandle" 
      style="width: 100%"
      stripe
    >
      <el-table-column 
        type="selection" 
        header-align="center" 
        align="center" 
        width="50"
      ></el-table-column>
      <el-table-column prop="id" label="ID" header-align="center" align="center" width="80"></el-table-column>
      <el-table-column prop="name" label="热点名" header-align="center" align="center" show-overflow-tooltip></el-table-column>
      <el-table-column prop="source" label="来源" header-align="center" align="center"></el-table-column>
      <el-table-column prop="attributeId" label="属性ID" header-align="center" align="center" width="90"></el-table-column>
      <el-table-column prop="url" label="信息链接" header-align="center" align="center" width="300">
        <template v-slot="scope">
          <template v-if="scope.row.url">
            <el-link
              type="primary"
              :href="scope.row.url"
              target="_blank"
              :underline="false"
              class="url-link"
            >
              {{ truncateUrl(scope.row.url) }}
            </el-link>
          </template>
          <template v-else>
            <span class="disabled-link">无链接</span>
          </template>
        </template>
      </el-table-column>
      <el-table-column prop="time" label="时间" header-align="center" align="center" width="180"></el-table-column>
      <el-table-column 
        label="操作" 
        fixed="right" 
        header-align="center" 
        align="center" 
        width="200"
      >
        <template v-slot="scope">
          <el-button 
            type="primary" 
            link 
            @click="addOrUpdateHandle(scope.row.id)"
            :icon="Edit"
            class="action-btn"
          >
            修改
          </el-button>
          <el-button 
            type="danger" 
            link 
            @click="state.deleteHandle(scope.row.id)"
            :icon="Delete"
            class="action-btn"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <el-pagination 
      :current-page="state.page" 
      :page-sizes="[10, 20, 50, 100]" 
      :page-size="state.limit" 
      :total="state.total" 
      layout="total, sizes, prev, pager, next, jumper" 
      @size-change="state.pageSizeChangeHandle" 
      @current-change="state.pageCurrentChangeHandle"
      style="margin-top: 20px;"
    ></el-pagination>
    
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update ref="addOrUpdateRef" @refreshDataList="state.getDataList"></add-or-update>
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { reactive, ref, toRefs } from "vue";
import AddOrUpdate from "./hotspot-add-or-update.vue";
import { Plus, Delete, Edit, Link } from '@element-plus/icons-vue';

const view = reactive({
  deleteIsBatch: true,
  getDataListURL: "/vsafety/hotspot/page",
  getDataListIsPage: true,
  exportURL: "/vsafety/hotspot/export",
  deleteURL: "/vsafety/hotspot"
});

const state = reactive({ ...useView(view), ...toRefs(view) });

const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number) => {
  addOrUpdateRef.value.init(id);
};

// 截断URL显示函数
const truncateUrl = (url: string): string => {
  if (!url) return '';
  const maxLength = 50; // 最大显示长度
  if (url.length <= maxLength) {
    return url;
  }
  return url.substring(0, maxLength) + '...';
};
</script>

<style lang="scss" scoped>
.mod-vsafety__hotspot {
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  
  .el-form {
    margin-bottom: 20px;
    
    .el-form-item {
      margin-bottom: 0;
    }
  }
  
  .el-table {
    margin-top: 10px;
    
    :deep(.el-table__cell) {
      transition: all 0.3s ease;
      padding: 12px 0;
    }
    
    :deep(.el-table__row) {
      height: 60px;
      
      &:hover {
        .el-table__cell {
          background-color: #ecf5ff !important;
        }
      }
    }
    
    :deep(.el-table__header .el-table__cell) {
      background-color: #f8f8f9;
      font-weight: bold;
    }
  }
  
  .action-btn {
    padding: 8px 10px;
    transition: all 0.3s;
    margin: 0 5px;
    font-size: 14px;
    
    .el-icon {
      transition: transform 0.3s;
      margin-right: 5px;
    }
    
    span {
      transition: all 0.3s;
    }
  }
  
  .el-pagination {
    justify-content: flex-end;
    margin-top: 20px;
  }
  
  .el-link {
    display: inline-flex;
    align-items: center;

    .el-icon {
      margin-right: 4px;
    }
  }

  /* URL链接样式 */
  .url-link {
    max-width: 100%;
    word-break: break-all;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;

    &:hover {
      text-decoration: underline;
    }
  }

  /* 新增：当链接为空时的样式 */
  .disabled-link {
    display: inline-flex;
    align-items: center;
    color: #c0c4cc; /* 较浅的颜色 */
    cursor: not-allowed;
  }
}
</style>