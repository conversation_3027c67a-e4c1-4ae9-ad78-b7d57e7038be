<template>
  <div class="mod-vsafety__picture">
    <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
      <!-- 新增的筛选查询组件 -->
      <el-form-item>
        <el-select v-model="state.dataForm.sentimentAnalysis" placeholder="请选择情感倾向性" clearable>
          <el-option label="正面" value="正面"></el-option>
          <el-option label="负面" value="负面"></el-option>
          <el-option label="中性" value="中性"></el-option>
        </el-select>
      </el-form-item>
      
      <el-form-item>
        <el-select v-model="state.dataForm.eventCategory" placeholder="请选择事件分类" clearable>
          <el-option label="政治舆情" value="政治舆情"></el-option>
          <el-option label="经济舆情" value="经济舆情"></el-option>
          <el-option label="文化舆情" value="文化舆情"></el-option>
          <el-option label="社会舆情" value="社会舆情"></el-option>
        </el-select>
      </el-form-item>
      
      <el-form-item>
        <el-button  type="primary" @click="state.getDataList()" :icon="Search">
          查询
        </el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="success" @click="resetQuery()" :icon="Refresh">
          重置
        </el-button>
      </el-form-item>
      <!-- 筛选查询组件结束 -->
      
      <el-form-item>
        <el-button type="primary" @click="addOrUpdateHandle()" :icon="Plus">
          新增
        </el-button>
      </el-form-item>
      <el-form-item>
        <el-button 
          type="danger" 
          @click="state.deleteHandle()" 
          :icon="Delete"
          :disabled="!state.dataListSelections.length"
        >
          删除
        </el-button>
      </el-form-item>
    </el-form>
    
    <el-table 
      v-loading="state.dataListLoading" 
      :data="state.dataList" 
      border 
      @selection-change="state.dataListSelectionChangeHandle" 
      style="width: 100%"
      stripe
    >
      <el-table-column 
        type="selection" 
        header-align="center" 
        align="center" 
        width="50"
      ></el-table-column>
      <el-table-column prop="id" label="序号" type=index header-align="center" align="center" width="120"></el-table-column>
      
      <!-- 图片预览列 -->
      <el-table-column label="图片预览" header-align="center" align="center">
        <template v-slot="scope">
          <div v-if="scope.row.picUrl" class="image-preview-container">
            <el-image 
              :src="scope.row.picUrl" 
              :preview-src-list="[scope.row.picUrl]"
              preview-teleported
              fit="cover"
              class="table-image-preview"
              hide-on-click-modal
              :z-index="3000"
            >
              <template #error>
                <div class="image-slot">
                  <el-icon><Picture /></el-icon>
                </div>
              </template>
            </el-image>
          </div>
          <span v-else>-</span>
        </template>
      </el-table-column>
      
      <el-table-column prop="detailId" label="关联事件ID" header-align="center" align="center"></el-table-column>
      
      <el-table-column label="图片分析" header-align="center" align="center" width="400">
        <template v-slot="scope">
          <div class="image-analysis-container">
            <!-- 只在没有分析结果时显示分析按钮 -->
            <el-button 
              v-if="!scope.row.analysisResult"
              type="primary" 
              @click="analyzeExistingImage(scope.row)"
              :loading="scope.row.analyzing"
              :disabled="!scope.row.picUrl"
            >
               分析图片
            </el-button>
          </div>
          
          <!-- 查看分析结果按钮 -->
          <el-button 
            v-if="scope.row.analysisResult"
            type="info" 
            @click="showAnalysisResult(scope.row)"
            link
          >
            查看分析结果
          </el-button>
        </template>
      </el-table-column>
      
      <el-table-column 
        label="操作" 
        fixed="right" 
        header-align="center" 
        align="center" 
        width="200"
      >
        <template v-slot="scope">
          <el-button 
            type="primary" 
            link 
            @click="addOrUpdateHandle(scope.row.id)"
            :icon="Edit"
            class="action-btn"
          >
            修改
          </el-button>
          <el-button 
            type="danger" 
            link 
            @click="state.deleteHandle(scope.row.id)"
            :icon="Delete"
            class="action-btn"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <el-pagination 
      :current-page="state.page" 
      :page-sizes="[10, 20, 50, 100]" 
      :page-size="state.limit" 
      :total="state.total" 
      layout="total, sizes, prev, pager, next, jumper" 
      @size-change="state.pageSizeChangeHandle" 
      @current-change="state.pageCurrentChangeHandle"
      style="margin-top: 20px;"
    ></el-pagination>
    
    <!-- 图片上传弹窗 -->
    <el-dialog 
      v-model="uploadDialogVisible" 
      title="上传图片" 
      width="30%"
      :close-on-click-modal="false"
    >
      <div class="upload-container">
        <input 
          ref="fileInputRef" 
          type="file" 
          accept="image/*" 
          @change="handleFileChange" 
          style="display: none"
        />
        <div class="upload-area" @click="triggerFileInput" v-if="!selectedFile">
          <el-icon class="upload-icon"><Plus /></el-icon>
          <p>点击选择图片文件</p>
        </div>
        <div class="file-selected" v-else>
          <el-icon class="file-icon"><Document /></el-icon>
          <div class="file-info">
            <p class="file-name">{{ selectedFileName }}</p>
            <p class="file-size">{{ formatFileSize(selectedFile.size) }}</p>
          </div>
          <el-icon class="change-icon" @click.stop="triggerFileInput"><Refresh /></el-icon>
        </div>
      </div>
      <template #footer>
        <el-button @click="uploadDialogVisible = false">取消</el-button>
        <el-button 
          type="primary" 
          @click="uploadPicture" 
          :loading="uploadLoading"
          :disabled="!selectedFile"
        >
          上传
        </el-button>
      </template>
    </el-dialog>
    
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update ref="addOrUpdateRef" @refreshDataList="state.getDataList"></add-or-update>
    
    <!-- 分析结果弹窗 -->
    <el-dialog
      v-model="analysisDialogVisible"
      width="75vw"
      :before-close="handleAnalysisDialogClose"
      :fullscreen="false"
      style="height: 80vh;margin-top: 10vh;"
      class="analysis-dialog"
    >
      <template #header>
        <div class="dialog-header">
          <span class="el-dialog__title">图片分析结果</span>
          <div class="dialog-header-actions">
            <el-button 
              v-if="currentAnalysisRow"
              type="primary" 
              @click="reanalyzeImage"
              :loading="currentAnalysisRow.analyzing"
              size="default"
              class="reanalyze-button"
              :icon="Refresh"
            >
              重新分析
            </el-button>
          </div>
        </div>
      </template>
      <div v-if="currentAnalysisResult" class="analysis-result-dialog">
        <!-- 上半部分：左右分区，使用flex容器确保对齐 -->
        <div class="upper-section-container">
          <!-- 左上半角（图片区） -->
          <div class="upper-section-left">
            <!-- 显示舆论情感和舆情分类 -->
            <div class="sentiment-category-info" v-if="getFirstEventInfo(currentAnalysisResult)">
              <div class="info-item">
                <span class="label" style="margin-left: 3%;">舆论情感:</span>
                <el-tag 
                  :type="getSentimentType(getFirstEventInfo(currentAnalysisResult).public_opinion_sentiment)" 
                  size="small"
                  style="margin-left: 7px;"
                >
                  {{ getFirstEventInfo(currentAnalysisResult).public_opinion_sentiment || '无' }}
                </el-tag>
                <span class="label" style="margin-left: 20px;">舆情分类:</span>
                <el-tag type="success" size="small" style="margin-left: 7px;">{{ getFirstEventInfo(currentAnalysisResult).public_opinion_category || '无' }}</el-tag>
              </div>
            </div>
            <p></p>
            
            <div class="image-section">
              <!-- 图片区 -->
              <el-image 
                v-if="currentFileUrl"
                :src="currentFileUrl"
                :preview-src-list="[currentFileUrl]"
                preview-teleported
                fit="cover"
                class="dialog-image-preview"
                hide-on-click-modal
                :z-index="3000"
              >
                <template #error>
                  <div class="image-slot">
                    <el-icon><Picture /></el-icon>
                  </div>
                </template>
              </el-image>
              
              <!-- 关键词 -->
              <div class="image-top-section">
                <div class="analysis-item-header">
                  <el-icon><Collection /></el-icon>
                  <span class="analysis-item-title">关键词</span>
                </div>
                <div v-if="parseTagsData(currentAnalysisResult).length > 0" class="tags-container">
                  <el-tag 
                    v-for="(tag, index) in parseTagsData(currentAnalysisResult)" 
                    :key="index" 
                    type="primary" 
                    size="large"
                    class="result-tag"
                  >
                    {{ tag }}
                  </el-tag>
                </div>
                <div v-else class="empty-placeholder">
                  <el-empty :image-size="60" description="暂无关键词数据" />
                </div>
              </div>
            </div>
          </div>
          
          <!-- 右上半角（分析结果区） -->
          <div class="upper-section-right">
            <div class="analysis-section analysis-section-scroll">
              <!-- 视觉元素 -->
              <div class="analysis-item">
                <div class="visual-elements-container">
                  <div v-if="parseVisualElementsData(currentAnalysisResult).length > 0">
                    <!-- 人物元素 -->
                    <div class="element-category" v-if="parseVisualElementsData(currentAnalysisResult).filter(el => el.element_type === '人物').length > 0">
                      <div class="category-header">
                        <el-icon><User /></el-icon>
                        <span class="category-title">人物 ({{ parseVisualElementsData(currentAnalysisResult).filter(el => el.element_type === '人物').length }})</span>
                      </div>
                      <div 
                        v-for="(element, index) in parseVisualElementsData(currentAnalysisResult).filter(el => el.element_type === '人物').slice(0, showAllElements ? undefined : 4)" 
                        :key="'person-'+index" 
                        class="element-item"
                        @mouseenter="highlightElement(element)"
                        @mouseleave="clearHighlight"
                      >
                        <div class="element-type">
                          <el-tag type="primary" size="small">
                            {{ element.element_type }}
                          </el-tag>
                        </div>
                        <div class="element-description">
                          {{ element.description }}
                        </div>
                        <div class="element-position">
                          <el-icon><Location /></el-icon>
                          <span>{{ element.position }}</span>
                        </div>
                      </div>
                    </div>
                    
                    <!-- 物体元素 -->
                    <div class="element-category" v-if="parseVisualElementsData(currentAnalysisResult).filter(el => el.element_type === '物体').length > 0">
                      <div class="category-header">
                        <el-icon><Box /></el-icon>
                        <span class="category-title">物体 ({{ parseVisualElementsData(currentAnalysisResult).filter(el => el.element_type === '物体').length }})</span>
                      </div>
                      <div 
                        v-for="(element, index) in parseVisualElementsData(currentAnalysisResult).filter(el => el.element_type === '物体').slice(0, showAllElements ? undefined : 4)" 
                        :key="'object-'+index" 
                        class="element-item"
                        @mouseenter="highlightElement(element)"
                        @mouseleave="clearHighlight"
                      >
                        <div class="element-type">
                          <el-tag type="success" size="small">
                            {{ element.element_type }}
                          </el-tag>
                        </div>
                        <div class="element-description">
                          {{ element.description }}
                        </div>
                        <div class="element-position">
                          <el-icon><Location /></el-icon>
                          <span>{{ element.position }}</span>
                        </div>
                      </div>
                    </div>
                    
                    <!-- 其他元素 -->
                    <div class="element-category" v-if="parseVisualElementsData(currentAnalysisResult).filter(el => el.element_type !== '人物' && el.element_type !== '物体').length > 0">
                      <div class="category-header">
                        <el-icon><Star /></el-icon>
                        <span class="category-title">其他 ({{ parseVisualElementsData(currentAnalysisResult).filter(el => el.element_type !== '人物' && el.element_type !== '物体').length }})</span>
                      </div>
                      <div 
                        v-for="(element, index) in parseVisualElementsData(currentAnalysisResult).filter(el => el.element_type !== '人物' && el.element_type !== '物体').slice(0, showAllElements ? undefined : 4)" 
                        :key="'other-'+index" 
                        class="element-item"
                        @mouseenter="highlightElement(element)"
                        @mouseleave="clearHighlight"
                      >
                        <div class="element-type">
                          <el-tag type="warning" size="small">
                            {{ element.element_type }}
                          </el-tag>
                        </div>
                        <div class="element-description">
                          {{ element.description }}
                        </div>
                        <div class="element-position">
                          <el-icon><Location /></el-icon>
                          <span>{{ element.position }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div v-else class="empty-placeholder">
                    <el-empty :image-size="80" description="暂无视觉元素数据" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 下半部分（事件详情区） -->
        <div class="events-section">
          <div class="events-header">
            <el-icon><Document /></el-icon>
            <span class="events-title">事件详情</span>
          </div>
          
          <div class="events-content">
            <div v-if="parseEventsData(currentAnalysisResult).length > 0">
              <div 
                v-for="(event, index) in parseEventsData(currentAnalysisResult)" 
                :key="index" 
                class="event-item"
              >
                <el-row :gutter="24">
                  <el-col :span="12">
                    <div class="event-info-item">
                      <div class="event-info-label">
                        <el-icon><Collection /></el-icon>
                        <span>事件名称:</span>
                      </div>
                      <div class="event-info-value">{{ event.event_title }}</div>
                    </div>
                    
                    <div class="event-info-item">
                      <div class="event-info-label">
                        <el-icon><Clock /></el-icon>
                        <span>事件时间:</span>
                      </div>
                      <div class="event-info-value">{{ event.time }}</div>
                    </div>
                    
                    <div class="event-info-item">
                      <div class="event-info-label">
                        <el-icon><Document /></el-icon>
                        <span>场景描述:</span>
                      </div>
                      <div class="event-info-value">{{ event.scene_description }}</div>
                    </div>
                    
                    <div 
                      class="event-info-item"
                      :class="{ 'expandable': event.event_description && event.event_description.length > 100 }"
                      @click="toggleDescription(index)"
                    >
                      <div class="event-info-label">
                        <el-icon><Document /></el-icon>
                        <span>事件描述:</span>
                      </div>
                      <div class="event-info-value">
                        <span v-if="!expandedDescriptions[index]">{{ event.event_description?.substring(0, 100) }}<span v-if="event.event_description?.length > 100">...</span></span>
                        <span v-else>{{ event.event_description }}</span>
                      </div>
                    </div>
                    
                    <!-- 新增 objects 字段 -->
                    <div class="event-info-item">
                      <div class="event-info-label">
                        <el-icon><Box /></el-icon>
                        <span>相关物品:</span>
                      </div>
                      <div class="event-info-value">
                        <template v-if="event.objects && event.objects.length > 0">
                          <el-tag 
                            v-for="(object, oIndex) in event.objects" 
                            :key="oIndex" 
                            size="small" 
                            class="participant-tag"
                          >
                            {{ object }}
                          </el-tag>
                        </template>
                        <span v-else>无</span>
                      </div>
                    </div>
                    <!-- 新增 negative_hotspot_category 字段 -->
                    <div class="event-info-item">
                      <div class="event-info-label">
                        <el-icon><Warning /></el-icon>
                        <span>负面热点:</span>
                      </div>
                      <div class="event-info-value">{{ event.negative_hotspot_category || '无' }}</div>
                    </div>
                    
                  </el-col>
                  
                  <el-col :span="12">
                    <div class="event-info-item">
                      <div class="event-info-label">
                        <el-icon><Location /></el-icon>
                        <span>事件地点:</span>
                      </div>
                      <div class="event-info-value">{{ event.location }}</div>
                    </div>
                    
                    <div class="event-info-item">
                      <div class="event-info-label">
                        <el-icon><User /></el-icon>
                        <span>参与者:</span>
                      </div>
                      <div class="event-info-value">
                        <template v-if="event.participants && event.participants.length > 0">
                          <el-tag 
                            v-for="(participant, pIndex) in event.participants" 
                            :key="pIndex" 
                            size="small" 
                            class="participant-tag"
                          >
                            {{ participant }}
                          </el-tag>
                        </template>
                        <span v-else>无</span>
                      </div>
                    </div>
                    
                    <div class="event-info-item">
                      <div class="event-info-label">
                        <el-icon><Warning /></el-icon>
                        <span>事件原因:</span>
                      </div>
                      <div class="event-info-value">{{ event.event_cause }}</div>
                    </div>
                    
                    <div class="event-info-item">
                      <div class="event-info-label">
                        <el-icon><TrendCharts /></el-icon>
                        <span>事件结果:</span>
                      </div>
                      <div class="event-info-value">{{ event.event_outcome }}</div>
                    </div>
                    <div class="event-info-item">
                      <div class="event-info-label">
                        <el-icon><Collection /></el-icon>
                        <span>详细分类:</span>
                      </div>
                      <div class="event-info-value">
                        <template v-if="event.detailed_category && event.detailed_category.length > 0">
                          <el-tag 
                            v-for="(category, cIndex) in event.detailed_category" 
                            :key="cIndex" 
                            size="small" 
                            class="participant-tag"
                            type="success"
                          >
                            {{ category }}
                          </el-tag>
                        </template>
                        <span v-else>无</span>
                      </div>
                    </div>
                  </el-col>
                </el-row>
              </div>
            </div>
            <div v-else class="empty-placeholder">
              <el-empty :image-size="80" description="暂无事件数据" />
            </div>
          </div>
        </div>
      </div>
      <div v-else>
        <el-empty description="暂无分析结果" />
      </div>

    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { reactive, ref, toRefs } from "vue";
import AddOrUpdate from "./picture-add-or-update.vue";
import baseService from "@/service/baseService";
import { ElMessage, ElLoading } from "element-plus";
import { Plus, Delete, Edit, Picture, Refresh, Collection, Search, Location, User, Box, Star, Clock, TrendCharts, Warning, Document } from '@element-plus/icons-vue';

const view = reactive({
  deleteIsBatch: true,
  getDataListURL: "/vsafety/picture/page",
  getDataListIsPage: true,
  exportURL: "/vsafety/picture/export",
  deleteURL: "/vsafety/picture"
});

const state = reactive({ 
  ...useView(view), 
  ...toRefs(view),
  // 重写 getDataList 方法
  getDataList() {
    // 判断是否需要调用分类查询接口
    if (state.dataForm?.sentimentAnalysis || state.dataForm?.eventCategory) {
      // 调用分类查询接口
      state.getCategoryList();
    } else {
      // 调用原始分页接口
      state.defaultGetList();
    }
  },
  // 新增分类查询方法
  async getCategoryList() {
    state.dataListLoading = true;
    try {
      const params = {
        page: state.page,
        limit: state.limit,
        orderField: state.orderField,
        order: state.order,
        ...state.dataForm
      };
      
      const res = await baseService.post("/vsafety/picture/categoryPage", params);
      if (res.code === 0) {
        state.dataList = res.data.list;
        state.total = res.data.total;
      } else {
        state.dataList = [];
        state.total = 0;
        ElMessage.error(res.msg);
      }
    } catch (error) {
      state.dataList = [];
      state.total = 0;
      ElMessage.error("请求失败");
    } finally {
      state.dataListLoading = false;
    }
  },
  // 保存原始 getList 方法
  defaultGetList: useView(view).getDataList
});

// 添加重置查询方法
const resetQuery = () => {
  // 确保 dataForm 存在并重置所有筛选条件
  if (!state.dataForm) {
    state.dataForm = {};
  }
  state.dataForm.sentimentAnalysis = '';
  state.dataForm.eventCategory = '';
  state.page = 1;
  // 调用修改后的getList方法而不是defaultGetList
  state.getDataList();
};

// 图片上传相关状态
const uploadDialogVisible = ref(false);
const fileInputRef = ref<HTMLInputElement | null>(null);
const selectedFile = ref<File | null>(null);
const selectedFileName = ref("");
const uploadLoading = ref(false);

// 添加弹窗相关状态
const analysisDialogVisible = ref(false);
const currentAnalysisResult = ref<string | null>(null);
const currentFileUrl = ref<string | null>(null);
const currentAnalysisRow = ref<any>(null);
const showAllElements = ref(false);
const expandedDescriptions = ref<{[key: number]: boolean}>({});

// 添加展开/收起描述的方法
const toggleDescription = (index: number) => {
  expandedDescriptions.value[index] = !expandedDescriptions.value[index];
};

// 添加高亮元素的方法（待实现具体功能）
const highlightElement = (element: any) => {
  // TODO: 实现图片上高亮显示对应部分的功能
  console.log('高亮元素:', element);
};

// 清除高亮的方法
const clearHighlight = () => {
  // TODO: 实现清除高亮的功能
  console.log('清除高亮');
};

// 分析现有图片
const analyzeExistingImage = async (row: any) => {
  try {
    row.analyzing = true;
    
    // 从URL获取图片文件
    const response = await fetch(row.picUrl);
    const blob = await response.blob();
    const file = new File([blob], row.picUrl.split('/').pop() || 'image', { type: blob.type });
    
    const formData = new FormData();
    formData.append('file', file);
    formData.append('id', row.id); // 添加id参数
    
    const res = await baseService.post("/vsafety/picture/analysis", formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
    
    if (res.code === 0) {
      // 获取最新数据
      const updatedData = await baseService.get(`/vsafety/picture/${row.id}`);
      if (updatedData.code === 0) {
        // 更新当前行的分析结果
        row.analysisResult = updatedData.data.analysisResult;
        ElMessage.success("图片分析完成");
      }
    } else {
      ElMessage.error(res.msg || "图片分析失败");
    }
  } catch (err) {
  
  } finally {
    row.analyzing = false;
  }
};

// 触发文件选择
const triggerFileInput = () => {
  if (fileInputRef.value) {
    fileInputRef.value.click();
  }
};

// 处理文件选择
const handleFileChange = (event: Event) => {
  const input = event.target as HTMLInputElement;
  if (input.files && input.files.length > 0) {
    const file = input.files[0];
    
    // 验证文件类型是否为图片文件
    const allowedTypes = [
      'image/jpeg', 'image/png', 'image/gif', 'image/bmp', 
      'image/webp', 'image/svg+xml'
    ];
    
    const allowedExtensions = [
      '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg'
    ];
    
    // 检查 MIME 类型或文件扩展名
    const isAllowedType = allowedTypes.includes(file.type);
    const isAllowedExtension = allowedExtensions.some(ext => 
      file.name.toLowerCase().endsWith(ext)
    );
    
    if (!isAllowedType && !isAllowedExtension) {
      ElMessage.error("请选择有效的图片文件 (jpg, jpeg, png, gif, bmp, webp, svg)");
      // 清空文件输入框
      input.value = '';
      return;
    }
    
    // 检查文件大小
    const isLt10M = file.size / 1024 / 1024 < 10;
    if (!isLt10M) {
      ElMessage.error('图片大小不能超过10MB!');
      input.value = '';
      return;
    }
    
    selectedFile.value = file;
    selectedFileName.value = file.name;
  }
};

// 上传图片文件
const uploadPicture = async () => {
  if (!selectedFile.value) {
    ElMessage.warning("请选择图片文件");
    return;
  }

  uploadLoading.value = true;
  
  try {
    const formData = new FormData();
    formData.append("file", selectedFile.value);
    
    const res = await baseService.post("/vsafety/picture/upload", formData);
    
    if (res.code === 0) {
      ElMessage.success("图片上传成功");
      uploadDialogVisible.value = false;
      // 刷新图片分页数据
      state.getDataList();
    } else {
      ElMessage.error(res.msg || "图片上传失败");
    }
  } catch (error: any) {
    ElMessage.error("图片上传失败: " + (error.message || "未知错误"));
  } finally {
    uploadLoading.value = false;
  }
};

// 格式化文件大小
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 通用数据解析函数
const parseData = (result: string, key: string): any[] => {
  try {
    // 如果result是字符串，先解析为JSON对象
    let parsed;
    if (typeof result === 'string') {
      parsed = JSON.parse(result);
    } else {
      parsed = result;
    }
    
    // 如果parsed是数组且第一个元素有指定的key
    if (Array.isArray(parsed) && parsed.length > 0 && parsed[0][key]) {
      return parsed[0][key];
    }
    
    // 如果parsed有指定的key且是数组，返回该key对应的值
    if (parsed[key] && Array.isArray(parsed[key])) {
      return parsed[key];
    }
    
    return [];
  } catch (error) {
    console.error(`解析${key}数据出错:`, error);
    return [];
  }
};

// 解析事件数据
const parseEventsData = (result: string): any[] => {
  return parseData(result, 'events');
};

// 解析视觉元素数据
const parseVisualElementsData = (result: string): any[] => {
  return parseData(result, 'visual_elements');
};

// 解析标签数据
const parseTagsData = (result: string): string[] => {
  return parseData(result, 'tags');
};

const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number) => {
  // 如果有id，则是修改操作，使用原来的弹窗
  if (id) {
    addOrUpdateRef.value.init(id);
  } else {
    // 否则是新增操作，打开上传弹窗
    uploadDialogVisible.value = true;
    selectedFile.value = null;
    selectedFileName.value = "";
  }
};

// 显示分析结果
const showAnalysisResult = async (row: any) => {
  // 如果有分析结果直接显示，否则先获取
  if (row.analysisResult) {
    currentAnalysisResult.value = row.analysisResult;
    currentFileUrl.value = row.picUrl;
    currentAnalysisRow.value = row; // 保存当前行数据
    analysisDialogVisible.value = true;
  } else {
    // 重新获取数据
    try {
      const res = await baseService.get(`/vsafety/picture/${row.id}`);
      if (res.code === 0 && res.data.analysisResult) {
        currentAnalysisResult.value = res.data.analysisResult;
        currentFileUrl.value = row.picUrl;
        currentAnalysisRow.value = row; // 保存当前行数据
        analysisDialogVisible.value = true;
      } else {
        ElMessage.warning("暂无分析结果");
      }
    } catch (err) {
      ElMessage.error("获取分析结果失败: " + (err as Error).message);
    }
  }
};

// 添加重新分析方法
const reanalyzeImage = async () => {
  if (!currentAnalysisRow.value) return;
  
  try {
    const row = currentAnalysisRow.value;
    row.analyzing = true;
    
    // 从URL获取图片文件
    const response = await fetch(row.picUrl);
    const blob = await response.blob();
    const file = new File([blob], row.picUrl.split('/').pop() || 'image', { type: blob.type });
    
    const formData = new FormData();
    formData.append('file', file);
    formData.append('id', row.id); // 添加id参数
    
    const res = await baseService.post("/vsafety/picture/analysis", formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
    
    if (res.code === 0) {
      // 获取最新数据
      const updatedData = await baseService.get(`/vsafety/picture/${row.id}`);
      if (updatedData.code === 0) {
        // 更新当前行的分析结果
        row.analysisResult = updatedData.data.analysisResult;
        ElMessage.success("图片分析完成");
        // 更新弹窗中的分析结果
        currentAnalysisResult.value = updatedData.data.analysisResult;
      }
    } else {
      ElMessage.error(res.msg || "图片分析失败");
    }
  } catch (err) {
    //ElMessage.error("图片分析失败: " + (err as Error).message);
  } finally {
    if (currentAnalysisRow.value) {
      currentAnalysisRow.value.analyzing = false;
    }
  }
};

// 处理分析弹窗关闭
const handleAnalysisDialogClose = () => {
  analysisDialogVisible.value = false;
};

// 添加获取第一个事件信息的方法
const getFirstEventInfo = (result: string): any => {
  const events = parseEventsData(result);
  return events.length > 0 ? events[0] : null;
};

// 添加获取情感类型的方法
const getSentimentType = (sentiment: string) => {
  switch (sentiment) {
    case '正面':
      return 'success';
    case '负面':
      return 'danger';
    case '中性':
      return 'warning';
    default:
      return 'info';
  }
};

</script>

<style lang="scss" scoped>
.mod-vsafety__picture {
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  
  .el-form {
    margin-bottom: 20px;
    
    .el-form-item {
      margin-bottom: 0;
    }
    
    // 添加表单项标签样式
    :deep(.el-form-item__label) {
      font-weight: normal;
      color: #606266;
    }
  }
  
  // 批量上传按钮样式
  .bulk-upload {
    display: inline-block;
    margin-left: 10px;
  }
  
  .el-table {
    margin-top: 10px;
    
    :deep(.el-table__cell) {
      transition: all 0.3s ease;
      padding: 12px 0;
    }
    
    :deep(.el-table__row) {
      height: 60px;
      
      &:hover {
        .el-table__cell {
          background-color: #ecf5ff !important;
        }
      }
    }
    
    :deep(.el-table__header .el-table__cell) {
      background-color: #f8f8f9;
      font-weight: bold;
    }
    
    .image-preview-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 8px;
      
      .table-image-preview {
        width: 140px;
        height: 84px;
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.3s;
        
        &:hover {
          transform: scale(1.05);
          box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        }
      }
      
      .image-link {
        font-size: 12px;
      }
    }
    
    :deep(.image-slot) {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;
      height: 100%;
      background: #f5f7fa;
      color: #909399;
      
      .el-icon {
        font-size: 20px;
      }
    }
    
    .image-analysis-container {
      display: flex;
      justify-content: center;
    }
  }
  
  .action-btn {
    padding: 8px 10px;
    transition: all 0.3s;
    margin: 0 5px;
    font-size: 14px;
    
    .el-icon {
      transition: transform 0.3s;
      margin-right: 5px;
    }
    
    span {
      transition: all 0.3s;
    }
  }
  
  .el-pagination {
    justify-content: flex-end;
    margin-top: 20px;
  }
  
  .el-link {
    display: inline-flex;
    align-items: center;
    
    .el-icon {
      margin-right: 4px;
    }
  }
  
  .upload-container {
    text-align: center;
    padding: 20px 0;
    
    .upload-area {
      border: 2px dashed #dcdfe6;
      border-radius: 6px;
      padding: 30px 0;
      cursor: pointer;
      transition: all 0.3s;
      margin-bottom: 20px;
      
      &:hover {
        border-color: #409eff;
        background-color: #f5f9ff;
      }
      
      .upload-icon {
        font-size: 48px;
        color: #c0c4cc;
        margin-bottom: 10px;
      }
      
      p {
        font-size: 14px;
        color: #606266;
        margin: 0;
      }
    }
    
    .file-selected {
      display: flex;
      align-items: center;
      border: 1px solid #dcdfe6;
      border-radius: 6px;
      padding: 15px;
      background-color: #f5f7fa;
      margin-bottom: 20px;

      .file-icon {
        font-size: 32px;
        color: #409eff;
        margin-right: 10px;
      }

      .file-info {
        flex: 1;
        text-align: left;

        .file-name {
          font-size: 14px;
          color: #303133;
          margin: 0;
          font-weight: 500;
          word-break: break-all;
        }

        .file-size {
          font-size: 12px;
          color: #909399;
          margin: 5px 0 0 0;
        }
      }

      .change-icon {
        font-size: 20px;
        color: #909399;
        cursor: pointer;
        transition: all 0.3s;
        padding: 5px;
        border-radius: 50%;

        &:hover {
          color: #409eff;
          background-color: #e4e7ed;
        }
      }
    }
  }
}

/* 折叠面板样式 */
:deep(.el-collapse) {
  border: none;
  
  .el-collapse-item__header {
    background-color: transparent;
    border: none;
    padding-left: 0;
    font-size: 12px;
    color: #409eff;
  }
  
  .el-collapse-item__wrap {
    background-color: transparent;
    border: none;
  }
  
  .el-collapse-item__content {
    padding: 0;
    font-size: 12px;
  }
}

// 添加弹窗内容样式
.analysis-result-dialog {
  overflow-y: auto; /* 只允许垂直滚动 */
  overflow-x: hidden; 
  max-height: calc(100vh - 300px); /* 限制最大高度 */
  
  // 隐藏滚动条
  &::-webkit-scrollbar {
    display: none;
  }
  
  // 兼容 Firefox
  scrollbar-width: none;
  // 兼容 IE
  -ms-overflow-style: none;
  
  pre {
    margin: 0;
    white-space: pre-wrap;
    word-break: break-all;
    font-family: inherit;
    max-height: 400px;
    overflow-y: auto;
    padding: 12px;
    background-color: #f5f7fa;
    border-radius: 4px;
    
    // 隐藏滚动条
    &::-webkit-scrollbar {
      display: none;
    }
    
    // 兼容 Firefox
    scrollbar-width: none;
    // 兼容 IE
    -ms-overflow-style: none;
  }

  // 上半部分容器，使用flex确保左右对齐
  .upper-section-container {
    display: flex;
    gap: 24px;
    margin-bottom: 24px;
    height: 400px; // 固定高度确保对齐
  }
  
  .upper-section-left {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 100%;
  }
  .upper-section-right {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 100%;
    /* 视觉元素部分顶部对齐图片区，减去舆论情感和舆情分类高度 */
    justify-content: flex-start;
  }
  
  .image-section {
    flex: 1; // 占满可用空间
    background-color: #f5f7fa;
    border-radius: 8px;
    padding: 16px;
    display: flex;
    flex-direction: column;
    gap: 16px;
    overflow: hidden; // 防止内容溢出
    
    .image-top-section {
      flex: 0 0 auto; // 不拉伸，保持内容高度
      
      .analysis-item-header {
        display: flex;
        align-items: center;
        
        .el-icon {
          margin-right: 8px;
          font-size: 16px;
          color: #409eff;
        }
        
        .analysis-item-title {
          font-weight: 600;
          font-size: 16px;
          color: #303133;
        }
      }
      
      .tags-container {
        padding: 4px 0;
        
        .result-tag {
          margin-right: 20px;
          margin-bottom: 8px;
          font-size: 13px;
        }
      }
      
      .empty-placeholder {
        :deep(.el-empty) {
          padding: 12px 0;
        }
      }
    }
    
    .dialog-image-preview {
      flex: 1; // 填充剩余空间
      width: 100%;
      min-height: 200px; // 设置最小高度
      border-radius: 6px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      object-fit: cover;
    }
    
    :deep(.image-slot) {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;
      height: 100%;
      background: #ffffff;
      color: #909399;
      border-radius: 6px;
      
      .el-icon {
        font-size: 48px;
      }
    }
  }
  
  .analysis-section {
    flex: 1; // 占满可用空间
    display: flex;
    flex-direction: column;
    height: 100%; // 确保高度与左侧一致
    
    .analysis-item {
      flex: 1;
      display: flex;
      flex-direction: column;
      margin-bottom: 0;
      background-color: #f5f7fa;
      border-radius: 8px;
      padding: 16px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
      height: 100%; // 继承父容器高度
    }
  }
  
  .analysis-section-scroll {
    overflow: hidden;
    height: 100%;
    /* 让视觉元素部分顶部对齐图片区，减去舆论情感和舆情分类显示部分的高度 */
    margin-top: 48px;
  }
  
  .visual-elements-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    max-height: 100%;
    overflow-y: auto;
    padding-right: 8px;
    
    // 隐藏滚动条
    &::-webkit-scrollbar {
      display: none;
    }
    
    // 兼容 Firefox
    scrollbar-width: none;
    // 兼容 IE
    -ms-overflow-style: none;
    
    .element-category {
      margin-bottom: 16px;
      
      .category-header {
        display: flex;
        align-items: center;
        margin-bottom: 12px;
        padding: 8px 12px;
        background-color: #ffffff;
        border-radius: 6px;
        font-weight: 600;
        
        .el-icon {
          margin-right: 8px;
          font-size: 16px;
          color: #409eff;
        }
        
        .category-title {
          font-size: 16px;
          color: #303133;
        }
      }
      
      .element-item {
        padding: 12px;
        margin-bottom: 12px;
        background-color: #ffffff;
        border-radius: 6px;
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
        transition: all 0.3s ease;
        cursor: pointer;
        
        &:hover {
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
          transform: translateY(-2px);
        }
        
        .element-type {
          margin-bottom: 10px;
        }
        
        .element-description {
          font-size: 15px;
          margin-bottom: 10px;
          font-weight: 500;
          color: #606266;
        }
        
        .element-position {
          display: flex;
          align-items: center;
          font-size: 13px;
          color: #909399;
          
          .el-icon {
            margin-right: 6px;
            font-size: 15px;
          }
        }
      }
      
      .element-item:last-child {
        margin-bottom: 0;
      }
    }
    
    .element-category:last-child {
      margin-bottom: 0;
    }
    
    .toggle-elements {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 12px;
      color: #409eff;
      font-size: 14px;
      cursor: pointer;
      border-radius: 6px;
      transition: background-color 0.3s;
      margin-top: 8px;
      
      &:hover {
        background-color: #ecf5ff;
      }
      
      .el-icon {
        margin-left: 6px;
        font-size: 14px;
      }
    }
    
    .empty-placeholder {
      :deep(.el-empty) {
        padding: 24px 0;
      }
    }
  }
  
  .events-section {
    background-color: #f5f7fa;
    border-radius: 8px;
    padding: 15px;
    
    .events-header {
      display: flex;
      align-items: center;
      margin-bottom: 15px;
      padding-bottom: 10px;
      border-bottom: 1px solid #e4e7ed;
      
      .el-icon {
        margin-right: 8px;
        font-size: 16px;
        color: #409eff;
      }

      .events-title {
        font-weight: bold;
        font-size: 18px; // 增大标题字体大小
        color: #303133;
      }
    }

    .events-content {
      .event-item {
        padding: 15px;
        margin-bottom: 15px;
        background-color: #ffffff;
        border-radius: 4px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

        .event-info-item {
          display: flex;
          margin-bottom: 16px;
          line-height: 1.6;
          
          &.expandable {
            cursor: pointer;
            
            .event-info-value {
              position: relative;
              
              .expand-toggle {
                color: #409eff;
                margin-left: 6px;
              }
            }
          }
          
          .event-info-label {
            display: flex;
            align-items: flex-start;
            width: 130px;
            font-weight: 600;
            flex-shrink: 0;
            color: #606266; /* 标签使用较浅的颜色 */
            
            .el-icon {
              margin-right: 6px;
              font-size: 16px;
              margin-top: 2px;
            }
          }
          
          .event-info-value {
            flex: 1;
            color: #303133; /* 值使用较深的颜色，形成对比 */
            font-size: 15px;
            
            .participant-tag {
              margin-right: 6px;
              margin-bottom: 4px;
            }
          }
        }
      }
    }

    .empty-placeholder {
      :deep(.el-empty) {
        padding: 20px 0;
      }
    }
  }
  
  position: relative;
}

.dialog-footer {
  text-align: right;
}

// 新增: 弹窗标题样式
.dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  
  .el-dialog__title {
    font-size: 20px;
    font-weight: bold;
  }
  
  .dialog-header-actions {
    flex: 1;
    display: flex;
    justify-content: right;
  }
}

.reanalyze-button {
  font-size: 14px;
  padding: 8px 16px;
}
</style>
